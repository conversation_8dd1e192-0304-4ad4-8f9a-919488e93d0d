import { Button } from "@/components/main/button";
import { CircleOutlineCloseIcon, UserInfoIcon } from "@/components/main/icon";
import Modal from "@/components/main/modal";
import { useLogout } from "@/controllers/API/queries/auth";
import { useAlert } from "@/hooks/useAlert";
import { cn } from "@/utils/utils";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "../../contexts/authContext";
import UserContent from "./component/userContent";

type UserEditModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

export default function UserEditModal({ open, setOpen }: UserEditModalProps) {
  const { userData } = useContext(AuthContext);
  const [curTab, setCurTab] = useState("userInfo");

  const alert = useAlert();
  const { mutate: mutationLogout } = useLogout();

  const tabItemCn =
    "flex w-full px-2 py-[5px] gap-1 items-center rounded-lg cursor-pointer text-text-3 text-sm hover:bg-bg-light-1";
  const tabItemActiveCn = "bg-bg-light-5 text-text-1 font-medium";

  useEffect(() => {}, [userData]);

  const handleLogout = () => {
    mutationLogout();
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-[700px] w-[1200px] bg-bg-light-1"
    >
      <Modal.Content className="flex h-full flex-row bg-bg-light-1 p-0 shadow-toastbg">
        <div
          className="fixed left-[calc(50%-680px)] top-[calc(50%-370px)] flex h-[60px] w-[60px] cursor-pointer flex-col items-center gap-1 rounded-xl border border-text-2-icon p-2"
          onClick={() => setOpen(false)}
        >
          <div className="w-full text-start">
            <CircleOutlineCloseIcon className="text-2xl text-text-2-icon" />
          </div>
          <div className="w-full text-start text-xs">ESC</div>
        </div>

        <div className="flex h-full w-[200px] flex-col gap-4 border-r border-border-1 bg-bg-light-3 px-6 py-4">
          <div className="text-base font-medium text-text-1">设置</div>
          <div className="flex h-[588px] flex-col gap-2 font-normal">
            <div
              className={cn(
                tabItemCn,
                curTab === "userInfo" && tabItemActiveCn,
              )}
              onClick={() => setCurTab("userInfo")}
            >
              <UserInfoIcon className="text-[20px] text-text-1" />
              <span className="text-base text-text-1">账户信息</span>
            </div>
          </div>
          <Button variant="destructive" onClick={handleLogout}>
            退出登录
          </Button>
        </div>

        <div>{curTab === "userInfo" && <UserContent />}</div>
      </Modal.Content>
    </Modal>
  );
}
