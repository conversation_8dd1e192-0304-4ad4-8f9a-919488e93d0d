import { Button } from "@/components/main/button";
import { CheckPassIcon } from "@/components/main/icon";
import { InputPassword } from "@/components/main/input/InputPassword";
import Modal from "@/components/main/modal";
import { AuthContext } from "@/contexts/authContext";
import { usePostSaveUser } from "@/controllers/API/queries/userInfo";
import { useAlert } from "@/hooks/useAlert";
import { rsaDecryptPassword, rsaEncryptPassword } from "@/utils/password";
import { classNames } from "@/utils/utils";
import { DialogClose } from "@radix-ui/react-dialog";
import * as Form from "@radix-ui/react-form";
import { useContext, useState } from "react";

type NewPasswordModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

// const checkPass =
//   "url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI4IiBoZWlnaHQ9IjYiIHZpZXdCb3g9IjAgMCA4IDYiIGZpbGw9ImN1cnJlbnRDb2xvciI+CjxwYXRoIGQ9Ik0wLjM5NjUyMiAzLjM0NjIzQzAuMjAxMjYgMy4xNTA5NyAwLjIwMTI2IDIuODM0MzggMC4zOTY1MjIgMi42MzkxMkMwLjU5MTc4NCAyLjQ0Mzg2IDAuOTA4MzY3IDIuNDQzODYgMS4xMDM2MyAyLjYzOTEyTDMuMjI0OTUgNC43NjA0NEw3LjExNDA0IDAuODcxMzU1QzcuMzA5MyAwLjY3NjA5MyA3LjYyNTg4IDAuNjc2MDkzIDcuODIxMTQgMC44NzEzNTVDOC4wMTY0MSAxLjA2NjYyIDguMDE2NDEgMS4zODMyIDcuODIxMTQgMS41Nzg0NkwzLjU3ODUgNS44MjExQzMuMzgzMjQgNi4wMTYzNyAzLjA2NjY2IDYuMDE2MzYgMi44NzE0IDUuODIxMUwwLjM5NjUyMiAzLjM0NjIzWiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==')";

export default function NewPasswordModal({
  open,
  setOpen,
}: NewPasswordModalProps) {
  const { userData, getUser } = useContext(AuthContext);

  const [isSaving, setIsSaving] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const rsaCurrentPassword = rsaDecryptPassword(userData?.password ?? "");

  // 密码强度检查
  const getPasswordStrength = (password: string) => {
    const hasMinLength = password.length >= 8;
    const hasNumber = /\d/.test(password);
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      hasMinLength,
      hasNumber,
      hasLetter,
      hasSpecialChar,
      isValid: hasMinLength && hasNumber && hasLetter,
    };
  };

  const passwordStrength = getPasswordStrength(newPassword);

  // 确认密码是否匹配
  const passwordsMatch = (value: string) => {
    return value === confirmPassword;
  };

  // 原密码是否匹配
  const passwordOldMatch = (value: string) => {
    return value === rsaCurrentPassword;
  };

  const alert = useAlert();
  const { mutate: mutateSaveUser } = usePostSaveUser();

  const handleSubmit = () => {
    if (open) {
      handleUpdate();
    }
  };

  const handleUpdate = () => {
    setIsSaving(true);

    mutateSaveUser(
      {
        password: rsaEncryptPassword(currentPassword),
        new_password: rsaEncryptPassword(newPassword),
      },
      {
        onSuccess: () => {
          alert.success("保存成功");
          setOpen(false);
          setIsSaving(false);
          getUser();
        },
        onError: () => {
          alert.error("保存失败");
          setIsSaving(false);
        },
      },
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[352px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        修改密码
      </Modal.Header>
      <Modal.Content>
        <Form.Field name="currentPassword" className="relative mb-6">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            原密码
          </Form.Label>
          <Form.Control asChild>
            <InputPassword
              placeholder="请输入原密码"
              value={currentPassword}
              password
              onChange={(value) => setCurrentPassword(value)}
              required
            />
          </Form.Control>
          <Form.Message
            className="field-invalid animate-field-invalid text-xs font-normal leading-5 text-red-1"
            match="valueMissing"
          >
            请输入原密码
          </Form.Message>
          <Form.Message
            className="field-invalid animate-field-invalid text-xs font-normal leading-5 text-red-1"
            match={passwordOldMatch}
          >
            原密码校验不通过，请重新输入
          </Form.Message>
        </Form.Field>
        <Form.Field name="newPassword" className="relative mb-6">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            新密码
          </Form.Label>
          <Form.Control asChild>
            <InputPassword
              placeholder="请输入新密码"
              value={newPassword}
              password
              onChange={(value) => setNewPassword(value)}
              required
            />
          </Form.Control>
          {/* 密码强度提示 */}
          <div className="mt-2 text-xs font-normal text-text-1">
            <div className="mb-1 font-medium">密码强度：</div>
            <div className="space-y-1 pl-3 text-xs text-text-4">
              <p
                className={classNames(
                  "flex items-center gap-1",
                  passwordStrength.hasMinLength ? "text-text-3" : "text-text-4",
                )}
              >
                {passwordStrength.hasMinLength ? (
                  <CheckPassIcon className="text-[10px] text-text-3" />
                ) : (
                  <span className="h-1 w-1 rounded-full bg-text-4 text-xs text-text-4" />
                )}
                至少8位字符
              </p>
              <p
                className={classNames(
                  "flex items-center gap-1",
                  passwordStrength.hasNumber ? "text-text-3" : "text-text-4",
                )}
              >
                {passwordStrength.hasNumber ? (
                  <CheckPassIcon className="text-[10px] text-text-3" />
                ) : (
                  <span className="h-1 w-1 rounded-full bg-text-4 text-xs text-text-4" />
                )}
                包含数字
              </p>
              <p
                className={classNames(
                  "flex items-center gap-1",
                  passwordStrength.hasLetter ? "text-text-3" : "text-text-4",
                )}
              >
                {passwordStrength.hasLetter ? (
                  <CheckPassIcon className="text-[10px] text-text-3" />
                ) : (
                  <span className="h-1 w-1 rounded-full bg-text-4 text-xs text-text-4" />
                )}
                包含字母
              </p>
              <p
                className={classNames(
                  "flex items-center gap-1",
                  passwordStrength.hasSpecialChar
                    ? "text-text-3"
                    : "text-text-4",
                )}
              >
                {passwordStrength.hasSpecialChar ? (
                  <CheckPassIcon className="text-[10px] text-text-3" />
                ) : (
                  <span className="h-1 w-1 rounded-full bg-text-4 text-xs text-text-4" />
                )}
                特殊字符（推荐）
              </p>
            </div>
          </div>
        </Form.Field>
        <Form.Field name="confirmPassword" className="relative">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            确认密码
          </Form.Label>
          <Form.Control asChild>
            <InputPassword
              placeholder="请输入确认密码"
              value={confirmPassword}
              password
              onChange={(value) => setConfirmPassword(value)}
              required
            />
          </Form.Control>
          <Form.Message
            className="field-invalid animate-field-invalid text-xs font-normal leading-5 text-red-1"
            match="valueMissing"
          >
            请输入确认密码
          </Form.Message>
          <Form.Message
            className="field-invalid animate-field-invalid text-xs font-normal leading-5 text-red-1"
            match={passwordsMatch}
          >
            两次密码输入不一致，请重新输入
          </Form.Message>
        </Form.Field>
      </Modal.Content>
      <Modal.Footer>
        <div className="flex w-full items-center justify-end gap-3">
          <DialogClose asChild>
            <Button variant="outline" data-testid="btn-cancel-modal">
              取消
            </Button>
          </DialogClose>
          <Button
            type={"submit"}
            loading={isSaving}
            disabled={
              !passwordStrength.isValid || !passwordsMatch(confirmPassword)
            }
          >
            确认修改
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
}
