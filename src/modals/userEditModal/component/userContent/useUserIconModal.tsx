import * as Form from "@radix-ui/react-form";
import { useContext, useEffect, useRef, useState } from "react";

import Modal from "@/components/main/modal";
import { AuthContext } from "@/contexts/authContext";
import { usePostSaveUser } from "@/controllers/API/queries/userInfo";
import { useFile } from "@/hooks/knowledge/useFile";
import { useAlert } from "@/hooks/useAlert";
import FileUploader from "./FileUploader";
import FilePreview from "./filePreview";

type NewUserIconModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

interface FilePreviewRef {
  handleSave: () => void;
  handleCancel: () => void;
}

export default function NewUserIconModal({
  open,
  setOpen,
}: NewUserIconModalProps) {
  const { userData, getUser } = useContext(AuthContext);

  const [isSaving, setIsSaving] = useState(false);
  const { files, addFiles, removeFile, updateFile, clearFiles } = useFile();

  const filePreviewRef = useRef<FilePreviewRef>(null);

  const alert = useAlert();
  const { mutate: mutateSaveUser } = usePostSaveUser();

  // 重置状态当模态框关闭时
  useEffect(() => {
    if (!open) {
      setTimeout(() => {
        filePreviewRef.current?.handleCancel();
        clearFiles();
      }, 300);
    }
  }, [open]);

  const handleSubmit = () => {
    if (open && filePreviewRef.current) {
      filePreviewRef.current?.handleSave();
    }
  };

  const handleUpdate = (image: string) => {
    setIsSaving(true);

    mutateSaveUser(
      {
        avatar: image,
      },
      {
        onSuccess: () => {
          alert.success("保存成功");
          setOpen(false);
          setIsSaving(false);
          getUser();
        },
        onError: () => {
          alert.error("保存失败");
          setIsSaving(false);
        },
      },
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[352px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        修改头像
      </Modal.Header>
      <Modal.Content className="px-6 py-4">
        {files.length > 0 ? (
          <FilePreview
            ref={filePreviewRef}
            files={files}
            onRemoveFile={removeFile}
            onSavePreview={handleUpdate}
          />
        ) : (
          <FileUploader files={files} onFilesAdded={addFiles} />
        )}
      </Modal.Content>
      <Modal.Footer
        submit={{
          submitLabel: "保存",
          loading: isSaving,
          disabled: !files.length,
        }}
      />
    </Modal>
  );
}
