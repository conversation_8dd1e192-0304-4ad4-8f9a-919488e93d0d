import * as Form from "@radix-ui/react-form";
import { useContext, useEffect, useState } from "react";

import { Input } from "@/components/main/input";
import Modal from "@/components/main/modal";
import { AuthContext } from "@/contexts/authContext";
import { usePostSaveUser } from "@/controllers/API/queries/userInfo";
import { useAlert } from "@/hooks/useAlert";
type NewUsernameModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

export default function NewPhoneModal({
  open,
  setOpen,
}: NewUsernameModalProps) {
  const { getUser } = useContext(AuthContext);
  const [phone, setPhone] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const alert = useAlert();
  const { mutate: mutateSaveUser } = usePostSaveUser();

  useEffect(() => {
    if (open) {
      setPhone("");
    }
  }, [open]);

  const handleSubmit = () => {
    if (open) {
      handleUpdate();
    }
  };

  const handleUpdate = () => {
    setIsSaving(true);

    mutateSaveUser(
      {
        phone: phone,
      },
      {
        onSuccess: () => {
          alert.success("保存成功");
          setOpen(false);
          setIsSaving(false);
          getUser();
        },
        onError: () => {
          alert.error("保存失败");
          setIsSaving(false);
        },
      },
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[352px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="px-6 py-3 text-base font-medium text-text-1">
        变更手机号
      </Modal.Header>
      <Modal.Content>
        <Form.Field name="phone" className="relative mb-6">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            新手机号
          </Form.Label>
          <Form.Control asChild>
            <Input
              placeholder="请输入新手机号"
              value={phone}
              pattern="^1[3-9]\d{9}$"
              onChange={(e) => setPhone(e.target.value)}
              required
            />
          </Form.Control>
          <Form.Message className="field-invalid" match="valueMissing">
            请输入新手机号
          </Form.Message>
          <Form.Message className="field-invalid" match="patternMismatch">
            请输入正确的手机号
          </Form.Message>
        </Form.Field>
      </Modal.Content>
      <Modal.Footer
        submit={{
          submitLabel: "保存",
          loading: isSaving,
        }}
      />
    </Modal>
  );
}
