import UploadIcon from "@/assets/upload.svg?react";
import { useAlert } from "@/hooks/useAlert";
import type { FileItem } from "@/types/knowledge";
import { cn } from "@/utils/utils";
import { useRef, useState } from "react";
import { validateFile } from "./utils";

interface FileUploaderProps {
  files: FileItem[];
  onFilesAdded: (newFiles: FileItem[]) => void;
}

export default function FileUploader({ onFilesAdded }: FileUploaderProps) {
  const alert = useAlert();
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const getFileNameAndExtension = (fileName: string) => {
    const lastDotIndex = fileName.lastIndexOf(".");
    const name = fileName.substring(0, lastDotIndex);
    const extension = fileName.substring(lastDotIndex);
    return [name, extension];
  };

  const processFiles = (fileList: FileList | null) => {
    if (!fileList) return;

    const newFiles: FileItem[] = [];

    const file = fileList[0];
    const validation = validateFile(file);

    if (!validation.valid) {
      alert.error(validation.reason || "文件验证失败");
      return;
    }

    const [name, extension] = getFileNameAndExtension(file.name);

    const fileItem: FileItem = {
      id: `file-${Date.now()}`,
      size: file.size,
      type: file.type,
      file: file,
      progress: 0,
      name,
      extension,
    };

    newFiles.push(fileItem);

    if (newFiles.length > 0) {
      onFilesAdded(newFiles);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    processFiles(e.dataTransfer.files);
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    processFiles(e.target.files);
  };

  return (
    <div
      ref={dropAreaRef}
      className={cn(
        "flex w-full cursor-pointer flex-col items-center justify-center gap-2 rounded-xl border border-dashed p-6 transition-colors",
        isDragging
          ? "border-primary-default bg-bg-primary-2"
          : "border-border-1 bg-bg-light-3",
      )}
      onClick={handleFileSelect}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        onChange={handleFileInputChange}
        accept=".jpeg,.jpg,.png"
      />
      <UploadIcon className="h-[120px] w-[120px]" />
      <p className="text-base font-medium text-text-1">
        点击选择文件或将文件拖放到此处
      </p>
      <div className="flex min-h-[60px] flex-col items-center justify-center text-xs leading-5 text-text-4">
        {isDragging ? (
          <p className="text-sm text-primary-default">松手进行添加</p>
        ) : (
          <>
            <p>支持以下文件格式：</p>
            <p>JEPG、JPG、PNG</p>
            <p>单个文件大小不超过 5 MB</p>
          </>
        )}
      </div>
    </div>
  );
}
