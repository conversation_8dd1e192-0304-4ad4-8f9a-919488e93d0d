import { BuildStatus } from "../../constants/enums";
import { VertexBuildTypeAPI } from "../../types/api";

export const getSpecificClassFromBuildStatus = (
  buildStatus: BuildStatus | undefined,
  validationStatus: VertexBuildTypeAPI | null,
  isBuilding: boolean,
): string => {
  let isInvalid = validationStatus && !validationStatus.valid;

  if (BuildStatus.BUILDING === buildStatus) {
    return "border-yellow-3 border-[1px] ring-[0.75px] ring-foreground";
  } else if ((isInvalid || buildStatus === BuildStatus.ERROR) && !isBuilding) {
    return "border-red-1-error border-[1px] ring-[0.75px] ring-destructive shadow-node-error";
  } else {
    return "";
  }
};
